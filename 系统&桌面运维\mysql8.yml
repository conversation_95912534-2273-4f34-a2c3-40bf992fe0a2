version: '3.8'

services:
  mysql8:
    image: mysql:8
    container_name: mysql8
    restart: unless-stopped
    
    # 环境变量
    environment:
      MYSQL_ROOT_PASSWORD: 5d41402abc4b2a76b9719d911017c592
      MYSQL_ROOT_HOST: '%'
      MYSQL_DEFAULT_AUTH: mysql_native_password
    
    # 端口映射
    ports:
      - "1308:3306"
    
    # 数据卷挂载
    volumes:
      - /home/<USER>/conf/my.cnf:/etc/mysql/my.cnf
      - /home/<USER>/data:/var/lib/mysql
      - /home/<USER>/logs:/var/log/mysql
    
    # 网络配置
    networks:
      - mysql-network

# 定义网络
networks:
  mysql-network:
    driver: bridge

# 定义数据卷（可选，如果想使用命名卷）
volumes:
  mysql8-data:
    driver: local
  mysql8-logs:
    driver: local