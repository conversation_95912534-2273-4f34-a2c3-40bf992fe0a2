## 镜像运行mysql8语句
先创建相关的文件夹（根据自己的文件目录来选择）
```bash
mkdir -p /home/<USER>/conf /home/<USER>/data /home/<USER>/logs
```
需要挂载目录（设置自己的目录）
为确保容器能够正常访问和写入这些目录，需要调整宿主机目录的权限
```bash
sudo chown -R 999:999 /home/<USER>/data
sudo chown -R 999:999 /home/<USER>/logs
sudo chown -R 999:999 /home/<USER>/conf
```
# 镜像运行mysql8语句
docker run -d \
  --name mysql8 \
  -e MYSQL_ROOT_PASSWORD=5d41402abc4b2a76b9719d911017c592 \
  -e MYSQL_ROOT_HOST=% \
  -e MYSQL_DEFAULT_AUTH=mysql_native_password \
  -p 1308:3306 \
  -v /home/<USER>/conf/my.cnf:/etc/mysql/my.cnf \
  -v /home/<USER>/data:/var/lib/mysql \
  -v /home/<USER>/logs:/var/log/mysql \
  mysql:8

| 参数 | 说明 |
|------|------|
| -d | 后台运行容器 |
| --name mysql8 | 给容器命名为 mysql8 |
| -e MYSQL_ROOT_PASSWORD=rootpassword | 设置 root 用户密码为 yourpassword |
| -p 1308:3306 | 映射宿主机的 1308 端口到容器的 3306 端口 |
| -v /home/<USER>/conf/my.cnf:/etc/mysql/my.cnf | 挂载宿主机的 MySQL 配置文件到容器内 |
| -v /home/<USER>/data:/var/lib/mysql | 挂载宿主机的 MySQL 数据目录到容器内 |
| -v /home/<USER>/logs:/var/log/mysql | 挂载宿主机的 MySQL 日志目录到容器内 |

## 镜像运行mysql5语句
```bash
docker run -d \
  --name mysql5 \
  --restart always \
  -e MYSQL_ROOT_PASSWORD=5d41402abc4b2a76b9719d911017c592 \
  -e MYSQL_ROOT_HOST=% \
  -p 1305:3306 \
  -v /home/<USER>/conf/my.cnf:/etc/mysql/my.cnf \
  -v /home/<USER>/data:/var/lib/mysql \
  -v /home/<USER>/logs:/var/log/mysql \
  mysql:5.7
```

| 参数 | 说明 |
|------|------|
| -d | 后台运行容器 |
| --name mysql5 | 给容器命名为 mysql5 |
| --restart always | 开机重启 |
| -e MYSQL_ROOT_PASSWORD=rootpassword | 设置 root 用户密码为 yourpassword |
| -p 1305:3306 | 映射宿主机的 1305 端口到容器的 3306 端口 |
| -v /home/<USER>/conf/my.cnf:/etc/mysql/my.cnf | 挂载宿主机的 MySQL 配置文件到容器内 |
| -v /home/<USER>/data:/var/lib/mysql | 挂载宿主机的 MySQL 数据目录到容器内 |
| -v /home/<USER>/logs:/var/log/mysql | 挂载宿主机的 MySQL 日志目录到容器内 |