# GitLab 管理命令大全

## 基础服务管理命令

### 启动、停止与重启

```bash
# 启动所有 GitLab 组件
sudo gitlab-ctl start

# 停止所有 GitLab 组件
sudo gitlab-ctl stop

# 重启所有 GitLab 组件
sudo gitlab-ctl restart

# 重新加载配置（不停止服务）
sudo gitlab-ctl reconfigure

# 查看所有服务状态
sudo gitlab-ctl status

# 查看服务日志
sudo gitlab-ctl tail
```

### 单独组件管理

```bash
# 启动单个组件（例如：nginx）
sudo gitlab-ctl start nginx

# 停止单个组件
sudo gitlab-ctl stop nginx

# 重启单个组件
sudo gitlab-ctl restart nginx

# 查看单个组件状态
sudo gitlab-ctl status nginx

# 查看单个组件日志
sudo gitlab-ctl tail nginx
```

## 系统维护命令

### 备份与恢复

```bash
# 创建完整备份（默认保存在 /var/opt/gitlab/backups）
sudo gitlab-backup create

# 创建备份并跳过某些组件（例如：跳过上传的文件和仓库）
sudo gitlab-backup create SKIP=uploads,repositories

# 恢复备份（例如：恢复 1631529425_2021_09_13_14.1.0_gitlab_backup.tar）
sudo gitlab-ctl stop puma
sudo gitlab-ctl stop sidekiq
sudo gitlab-backup restore BACKUP=1631529425_2021_09_13_14.1.0
sudo gitlab-ctl restart

# 设置备份权限
sudo chown -R git:git /var/opt/gitlab/backups
```

### 数据库维护

```bash
# 进入 PostgreSQL 控制台
sudo gitlab-psql

# 进入 PostgreSQL 控制台（指定数据库）
sudo gitlab-psql -d gitlabhq_production

# 检查数据库状态
sudo gitlab-ctl pg-upgrade -c

# 优化数据库
sudo gitlab-rake db:optimize
```

### 系统检查与修复

```bash
# 检查 GitLab 环境
sudo gitlab-rake gitlab:env:info

# 检查 GitLab 系统状态
sudo gitlab-rake gitlab:check

# 检查文件系统权限
sudo gitlab-rake gitlab:check SANITIZE=true

# 清理 Redis 缓存
sudo gitlab-rake cache:clear

# 重建授权
sudo gitlab-rake gitlab:shell:setup

# 清理未引用的数据
sudo gitlab-rake gitlab:cleanup:orphan_job_artifact_files
sudo gitlab-rake gitlab:cleanup:orphan_lfs_file_references
```

## 用户与权限管理

```bash
# 解锁用户（例如：admin）
sudo gitlab-rake "gitlab:users:unlock[admin]"

# 修改用户密码
sudo gitlab-rake "gitlab:password:reset[admin]"

# 将用户设为管理员
sudo gitlab-rake "gitlab:users:set_admin[username]"

# 创建根用户令牌
sudo gitlab-rake "gitlab:personal_access_token:create"
```

## 仓库维护命令

```bash
# 检查仓库完整性
sudo gitlab-rake gitlab:git:fsck

# 重建 Gitaly 缓存
sudo gitlab-rake gitlab:gitaly:check

# 重建项目授权
sudo gitlab-rake gitlab:project_authorizations:repair

# 清理陈旧的仓库数据
sudo gitlab-rake gitlab:cleanup:project_uploads
sudo gitlab-rake gitlab:cleanup:remote_upload_files
```

## 系统升级与维护

```bash
# 检查版本
sudo gitlab-ctl version

# 升级前检查
sudo gitlab-rake gitlab:check

# 升级后迁移
sudo gitlab-rake db:migrate

# 清理旧版本包（Debian/Ubuntu）
sudo apt autoremove

# 清理旧版本包（CentOS/RHEL）
sudo yum autoremove
```

## 性能调优与监控

```bash
# 查看系统资源使用情况
sudo gitlab-ctl top

# 生成性能报告
sudo gitlab-rake gitlab:performance:metrics

# 检查 Sidekiq 队列
sudo gitlab-rake gitlab:sidekiq:check

# 清理 Sidekiq 队列
sudo gitlab-rake gitlab:sidekiq:clear

# 查看 Puma 状态
sudo gitlab-ctl puma-status
```

## 日志管理

```bash
# 查看所有日志
sudo gitlab-ctl tail

# 查看特定组件日志
sudo gitlab-ctl tail nginx
sudo gitlab-ctl tail postgresql
sudo gitlab-ctl tail sidekiq
sudo gitlab-ctl tail puma

# 查看最近的错误日志
sudo gitlab-ctl tail --follow --lines=100 puma | grep -i error

# 查看 GitLab Rails 日志
sudo tail -f /var/log/gitlab/gitlab-rails/production.log

# 查看 GitLab 审计日志
sudo tail -f /var/log/gitlab/gitlab-rails/audit_json.log
```

## Docker 环境下的 GitLab 命令

```bash
# 启动 GitLab 容器
docker start gitlab

# 停止 GitLab 容器
docker stop gitlab

# 重启 GitLab 容器
docker restart gitlab

# 查看 GitLab 容器日志
docker logs -f gitlab

# 进入 GitLab 容器
docker exec -it gitlab bash

# 在容器内执行 GitLab 命令
docker exec -it gitlab gitlab-ctl status
docker exec -it gitlab gitlab-rake gitlab:check
```

## 配置文件管理

```bash
# 编辑主配置文件
sudo vim /etc/gitlab/gitlab.rb

# 应用配置更改
sudo gitlab-ctl reconfigure

# 检查配置语法
sudo gitlab-ctl chef-client --help

# 备份配置文件
sudo cp /etc/gitlab/gitlab.rb /etc/gitlab/gitlab.rb.$(date +%Y%m%d)

# 恢复默认配置
sudo gitlab-ctl reset

# 查看当前配置
sudo gitlab-ctl show-config
```

## 故障排除命令

```bash
# 检查 GitLab 健康状态
curl -k https://localhost/-/health

# 检查 GitLab 就绪状态
curl -k https://localhost/-/readiness

# 检查 GitLab 活跃状态
curl -k https://localhost/-/liveness

# 重置 GitLab 两步验证
sudo gitlab-rake gitlab:two_factor:disable_for_all_users

# 修复仓库钩子
sudo gitlab-rake gitlab:shell:build_missing_hooks

# 重建授权索引
sudo gitlab-rake gitlab:elastic:rebuild_indices
```

## 高级维护命令

```bash
# 进入 Rails 控制台
sudo gitlab-rails console

# 进入 Rails 生产环境控制台
sudo gitlab-rails console -e production

# 退出 Rails 控制台
# 在控制台中输入以下命令
exit
# 或者使用快捷键
# Ctrl+D

# 检查 LDAP 配置
sudo gitlab-rake gitlab:ldap:check

# 迁移 CI 数据
sudo gitlab-rake gitlab:ci:migrate

# 清理陈旧的 CI 构建
sudo gitlab-rake gitlab:cleanup:stale_ci_builds

# 重新生成 Let's Encrypt 证书
sudo gitlab-ctl renew-le-certs
```

## 系统资源管理

```bash
# 查看磁盘使用情况
sudo du -sh /var/opt/gitlab

# 查看各组件磁盘使用情况
sudo du -sh /var/opt/gitlab/*

# 清理临时文件
sudo gitlab-ctl cleanup

# 压缩数据库
sudo gitlab-rake gitlab:db:reindex

# 清理会话
sudo gitlab-rake gitlab:cleanup:sessions
```

## 安全相关命令

```bash
# 检查安全漏洞
sudo gitlab-rake gitlab:security:all

# 生成自签名证书
sudo gitlab-ctl renew-self-signed-certs

# 检查 SSL 配置
sudo gitlab-ctl show-config | grep ssl

# 重置 Root 密码
sudo gitlab-rake "gitlab:password:reset[root]"

# 检查密码策略
sudo gitlab-rake gitlab:password_policy:check
```

## 常见问题解决方案

### 502 错误修复
```bash
# 重启 GitLab
sudo gitlab-ctl restart

# 如果仍然存在问题，重启 Puma/Unicorn
sudo gitlab-ctl restart puma

# 检查 Nginx 配置
sudo gitlab-ctl reconfigure
```

### 仓库访问问题
```bash
# 修复仓库权限
sudo gitlab-rake gitlab:check SANITIZE=true

# 重建 Gitaly 配置
sudo gitlab-ctl restart gitaly
```

### 内存不足问题
```bash
# 调整 Puma 工作进程数
# 编辑 /etc/gitlab/gitlab.rb
# puma['worker_processes'] = 2

# 应用更改
sudo gitlab-ctl reconfigure
```

### 数据库迁移问题
```bash
# 强制运行迁移
sudo gitlab-rake db:migrate:status
sudo gitlab-rake db:migrate RAILS_ENV=production
```

### 备份恢复失败
```bash
# 检查备份文件权限
sudo chown git:git /var/opt/gitlab/backups/*.tar

# 使用强制模式恢复
sudo gitlab-backup restore BACKUP=timestamp_of_backup force=yes
```

## 定期维护计划示例

### 每日维护
```bash
# 检查服务状态
sudo gitlab-ctl status

# 检查日志错误
sudo grep -i "error\|fatal\|critical" /var/log/gitlab/gitlab-rails/production.log
```

### 每周维护
```bash
# 创建备份
sudo gitlab-backup create

# 检查系统状态
sudo gitlab-rake gitlab:check
```

### 每月维护
```bash
# 更新 GitLab
sudo apt update && sudo apt upgrade gitlab-ce

# 清理未使用的数据
sudo gitlab-rake gitlab:cleanup:orphan_job_artifact_files
```

### 每季度维护
```bash
# 数据库优化
sudo gitlab-rake db:optimize

# 检查安全更新
sudo gitlab-rake gitlab:security:all
```