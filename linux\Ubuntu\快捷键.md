# Ubuntu 常用快捷键

## 系统操作
- `Ctrl + Alt + T`：打开终端
- `Alt + Tab`：切换窗口
- `Alt + F2`：运行命令
- `Ctrl + Alt + L`：锁屏
- `Ctrl + Alt + Del`：注销
- `Super（Win键）`：打开活动概览/应用菜单
- `Super + A`：显示所有应用
- `Super + D`：显示桌面

## 窗口管理
- `Super + 上/下/左/右`：窗口最大化/还原/左右分屏
- `Alt + F4`：关闭当前窗口
- `Alt + F10`：最大化窗口
- `Alt + F7`：移动窗口
- `Alt + F8`：调整窗口大小

## 工作区
- `Ctrl + Alt + 上/下`：切换工作区
- `Shift + Ctrl + Alt + 上/下`：将窗口移动到其他工作区

## 剪切板与文本
- `Ctrl + C`：复制
- `Ctrl + X`：剪切
- `Ctrl + V`：粘贴
- `Ctrl + Z`：撤销
- `Ctrl + Shift + V`：终端中粘贴

## 终端专用
- `Ctrl + Shift + T`：新建标签页
- `Ctrl + Shift + N`：新建终端窗口
- `Ctrl + Shift + C`：终端复制
- `Ctrl + Shift + V`：终端粘贴
- `Ctrl + L`：清屏
- `Ctrl + D`：关闭终端标签页/退出登录
## 输入法切换
- `Super + Space`：切换输入法（如中英文）
- `Ctrl + Space`：部分输入法（如 Fcitx、ibus）也可用此组合切换

---
如需自定义快捷键，可在“设置-键盘-快捷键”中进行配置。
