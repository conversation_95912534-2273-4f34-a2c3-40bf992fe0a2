# Linux 指令大全

## 文件与目录操作
- `ls`：列出目录内容。
    - `ls -l`：以长格式显示文件信息。
    - `ls -a`：显示所有文件，包括隐藏文件。
- `cd`：切换目录。
    - 示例：`cd /home/<USER>
- `pwd`：显示当前工作目录。
- `mkdir`：创建目录。
    - 示例：`mkdir new_folder`。
    - 一次性创建多个目录：`mkdir dir1 dir2 dir3`。
    - `mkdir -p`：递归创建目录。
        - 示例：`mkdir -p path/to/new_folder`。
        - 批量创建多级目录：`mkdir -p parent_folder/child_folder1 parent_folder/child_folder2`。
    - `mkdir -m`：设置目录权限。
- `touch`：创建空文件。
    - 示例：`touch new_file.txt`。
    - `touch -t`：设置文件时间戳。
    - `touch -m`：修改文件时间戳。
    - `touch -a`：修改文件访问时间戳。
    - `touch -c`：仅当文件存在时修改时间戳。
    - `touch -r`：使用指定文件的时间戳。
- `rmdir`：删除空目录。
- `rm`：删除文件或目录。
    - `rm -r`：递归删除目录及其内容。
- `cp`：复制文件或目录。
    - 示例：`cp source.txt destination.txt`。
    - 复制多个文件到目录：`cp file1.txt file2.txt /path/to/destination/`。
    - 复制目录：`cp -r source_dir/ destination_dir/`。
    - 复制目录下所有文件到另一个目录：`cp -r source_dir/* target_dir/`。
    - 提示覆盖：`cp -i source.txt destination.txt`。
    - 保留文件属性：`cp -p source.txt destination.txt`。
    - 显示复制进度：`cp -v source.txt destination.txt`。
    - 不覆盖已存在的文件：`cp -n source.txt destination.txt`。
    - 使用符号链接：`cp -s source.txt destination.txt`。
- `mv`：移动或重命名文件。
    - 示例：`mv old_name.txt new_name.txt`。

## 文件内容查看
- `cat`：查看文件内容。
- `tac`：反向查看文件内容。
- `more`：分页查看文件内容。
- `less`：分页查看文件内容（支持上下滚动）。
- `head`：查看文件开头部分。
    - 示例：`head -n 10 file.txt`。
- `tail`：查看文件末尾部分。
    - 示例：`tail -n 10 file.txt`。
- `nano` 或 `vim`：编辑文件。

## 权限管理
- `chmod`：修改文件权限。
    - 示例：`chmod 755 file.txt`。
- `chown`：更改文件所有者。
    - 示例：`chown user:group file.txt`。
- `chgrp`：更改文件所属组。

## 系统管理
- `top`：实时显示系统资源使用情况。
- `htop`：增强版的 `top`（需安装）。
- `ps`：查看当前运行的进程。
    - 示例：`ps aux`。
- `kill`：终止进程。
    - 示例：`kill -9 PID`。
- `df`：查看磁盘使用情况。
    - 示例：`df -h`。
- `du`：查看目录或文件大小。
    - 示例：`du -sh folder/`。
- `free`：查看内存使用情况。
    - 示例：`free -h`。

## 网络相关
- `ping`：测试网络连通性。
    - 示例：`ping google.com`。
- `ifconfig` 或 `ip addr`：查看或配置网络接口。
- `netstat`：查看网络连接状态。
- `curl`：发送网络请求。
    - 示例：`curl http://example.com`。
- `wget`：下载文件。
    - 示例：`wget http://example.com/file.zip`。

## 压缩与解压
- `tar`：打包与解压。
    - 打包：`tar -cvf archive.tar files/`。
    - 解压：`tar -xvf archive.tar`。
    - 压缩：`tar -czvf archive.tar.gz files/`。
    - 解压缩：`tar -xzvf archive.tar.gz`。
- `zip`：压缩文件。
    - 示例：`zip archive.zip files/`。
- `unzip`：解压缩文件。
    - 示例：`unzip archive.zip`。

## 搜索与查找
- `find`：查找文件。
    - 示例：`find /path -name "file.txt"`。
- `grep`：搜索文件内容。
    - 示例：`grep "keyword" file.txt`。
- `locate`：快速查找文件（需安装并更新数据库）。

## 用户管理
- `whoami`：显示当前用户。
- `id`：显示用户 ID 信息。
- `adduser` 或 `useradd`：添加用户。
- `passwd`：修改用户密码。
- `deluser` 或 `userdel`：删除用户。


## 软件管理
- `apt`（Debian/Ubuntu 系统）：
    - 更新软件源：`sudo apt update`。
    - 升级系统：`sudo apt upgrade`。
    - 安装软件：`sudo apt install package_name`。
    - 删除软件：`sudo apt remove package_name`。
- `yum` 或 `dnf`（CentOS/RHEL 系统）：
    - 安装软件：`sudo yum install package_name`。
    - 删除软件：`sudo yum remove package_name`。

## 其他常用命令
- `echo`：打印文本到终端或文件。
    - 示例：`echo "Hello, World!" > file.txt`。
- `date`：显示当前日期和时间。
- `uptime`：查看系统运行时间。
- `history`：查看命令历史记录。
- `alias`：创建命令别名。
    - 示例：`alias ll='ls -la'`。
## 系统相关
- `uname`：显示系统信息。
- `who`：显示当前登录用户。
- `last`：显示用户登录历史。
- `reboot`：重启系统。
以上是常用的 Linux 指令，具体使用时请参考 `man` 命令获取详细帮助，例如 `man ls`。