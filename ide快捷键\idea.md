# IntelliJ IDEA 快捷键大全

## 快捷键查询
- **Ctrl + Shift + A**：打开 "Find Action" 窗口，可以快速搜索所有操作和快捷键。

## 常用快捷键
### 编辑相关
- **Ctrl + Space**：代码补全。
- **Ctrl + Shift + Enter**：智能补全代码。
- **Ctrl + D**：复制当前行或选中内容。
- **Ctrl + Y**：删除当前行。
- **Ctrl + / 或 Ctrl + Shift + /**：单行注释/多行注释。
- **Ctrl + W**：扩展选中范围。
- **Ctrl + Shift + W**：缩小选中范围。
- **Ctrl + Shift + V**：从剪贴板历史中粘贴。
- **Ctrl + Alt + T**：用代码块包裹选中内容。
- **Ctrl + Q**：显示快速文档。

### 导航与搜索
- **Ctrl + N**：查找类。
- **Ctrl + Shift + N**：查找文件。
- **Ctrl + B 或 Ctrl + Click**：跳转到声明或定义。
- **Ctrl + Alt + B**：跳转到实现。
- **Ctrl + Shift + I**：快速查看定义。
- **Alt + 左/右箭头**：切换编辑标签。
- **Ctrl + E**：最近打开的文件。
- **Ctrl + Shift + E**：最近编辑的文件。
- **Ctrl + F**：在文件中查找。
- **Ctrl + Shift + F**：全局查找。
- **Ctrl + R**：在文件中替换。
- **Ctrl + Shift + R**：全局替换。
- **Shift 两次**：搜索任何内容。

### 调试相关
- **F8**：跳过（Step Over）。
- **F7**：进入（Step Into）。
- **Shift + F7**：智能进入（Smart Step Into）。
- **Shift + F8**：跳出（Step Out）。
- **Alt + F9**：运行到光标处。
- **Alt + F8**：计算表达式。
- **Ctrl + F8**：切换断点。
- **Ctrl + Shift + F8**：查看断点。

### 重构
- **Ctrl + Alt + L**：格式化代码。
- **Shift + F6**：重命名。
- **Ctrl + Alt + M**：提取方法。
- **Ctrl + Alt + V**：提取变量。
- **Ctrl + Alt + F**：提取字段。
- **Ctrl + Alt + C**：提取常量。
- **Ctrl + Alt + P**：提取参数。

### 版本控制
- **Alt + `**：打开版本控制操作菜单。
- **Ctrl + K**：提交代码。
- **Ctrl + T**：更新代码。
- **Ctrl + Shift + K**：推送代码。
- **Ctrl + Alt + Z**：回滚更改。
- **Shift + Alt + C**：查看最近的更改。

### 其他
- **Ctrl + Alt + S**：打开设置。
- **Alt + Enter**：快速修复提示。
- **Ctrl + Tab**：切换标签和工具窗口。
- **Ctrl + Shift + Backspace**：导航到上次编辑位置。
- **Ctrl + H**：显示类层次结构。
- **Ctrl + Shift + H**：显示方法层次结构。
- **Ctrl + Alt + H**：显示调用层次结构。

## 自定义快捷键
- **File > Settings > Keymap**：可以查看和修改快捷键。

以上快捷键适用于 Windows 系统，Mac 用户可将 `Ctrl` 替换为 `Command`，`Alt` 替换为 `Option`。