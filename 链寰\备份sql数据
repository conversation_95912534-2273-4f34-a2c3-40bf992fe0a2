-- 修改一个月后的wp的文章数据库链接为新域名：
UPDATE wp_options SET option_value = REPLACE(option_value, 'http://novelink.strlink.us', 'https://strlink.us') WHERE option_name IN ('home','siteurl');
UPDATE wp_posts   SET guid         = REPLACE(guid,         'http://novelink.strlink.us', 'https://strlink.us');
UPDATE wp_posts   SET post_content = REPLACE(post_content, 'http://novelink.strlink.us', 'https://strlink.us');
UPDATE wp_postmeta SET meta_value  = REPLACE(meta_value,   'http://novelink.strlink.us', 'https://strlink.us');
UPDATE wp_site    SET domain       = 'strlink.us' WHERE domain = 'novelink.strlink.us';
UPDATE wp_blogs   SET domain       = 'strlink.us' WHERE domain = 'novelink.strlink.us';
UPDATE wp_sitemeta SET meta_value = REPLACE(meta_value, 'http://novelink.strlink.us', 'https://strlink.us') WHERE meta_key = 'siteurl';









L60978031x``
L60978031x``