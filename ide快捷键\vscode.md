# VSCode 常用快捷键

## 常用快捷键
| 功能                     | 快捷键（Windows）       | 快捷键（Mac）          |
|--------------------------|-------------------------|------------------------|
| 打开命令面板             | `Ctrl+Shift+P`         | `Cmd+Shift+P`          |
| 打开文件                 | `Ctrl+O`               | `Cmd+O`                |
| 保存文件                 | `Ctrl+S`               | `Cmd+S`                |
| 全部保存                 | `Ctrl+K S`             | `Cmd+Option+S`         |
| 关闭编辑器               | `Ctrl+W`               | `Cmd+W`                |
| 分屏编辑                 | `Ctrl+\`               | `Cmd+\`                |
| 切换编辑器               | `Ctrl+Tab`             | `Cmd+Tab`              |
| 查找                     | `Ctrl+F`               | `Cmd+F`                |
| 全局查找                 | `Ctrl+Shift+F`         | `Cmd+Shift+F`          |
| 替换                     | `Ctrl+H`               | `Cmd+Option+F`         |
| 跳转到定义               | `F12`                  | `F12`                  |
| 打开终端                 | `` Ctrl+` ``           | `` Cmd+` ``            |
| 注释/取消注释            | `Ctrl+/`               | `Cmd+/`                |
| 格式化代码               | `Shift+Alt+F`          | `Shift+Option+F`       |
| 格式化 JSON              | `Alt+Shift+F`          | `Option+Shift+F`       |
| 显示大纲                 | `Ctrl+Shift+O`         | `Cmd+Shift+O`          |
| 调试运行                 | `F5`                   | `F5`                   |
| 停止调试                 | `Shift+F5`             | `Shift+F5`             |
| 删除当前行               | `Ctrl+Shift+K`         | `Cmd+Shift+K`          |
| 复制当前行               | `Alt+Shift+Down`       | `Option+Shift+Down`    |
| 上移当前行               | `Alt+Up`               | `Option+Up`            |
| 下移当前行               | `Alt+Down`             | `Option+Down`          |
| 选择当前行               | `Ctrl+L`               | `Cmd+L`                |
| 折叠代码块               | `Ctrl+Shift+[`         | `Cmd+Option+[`         |
| 展开代码块               | `Ctrl+Shift+]`         | `Cmd+Option+]`         |
| 折叠所有代码块           | `Ctrl+K Ctrl+0`        | `Cmd+K Cmd+0`          |
| 展开所有代码块           | `Ctrl+K Ctrl+J`        | `Cmd+K Cmd+J`          |

## 快捷键设置
1. 打开快捷键设置：`Ctrl+K Ctrl+S`（Windows）或 `Cmd+K Cmd+S`（Mac）。
2. 搜索需要修改的功能名称。
3. 点击右侧的铅笔图标，设置新的快捷键。
4. 保存后即可生效。

更多快捷键可参考 [VSCode 官方文档](https://code.visualstudio.com/docs/getstarted/keybindings)。