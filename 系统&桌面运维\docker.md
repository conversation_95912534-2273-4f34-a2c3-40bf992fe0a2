# 一、win11 docker 安装

## 1. 启用 WSL2 功能
```powershell
dism.exe /online /enable-feature /featurename:Microsoft-Windows-Subsystem-Linux /all /norestart
dism.exe /online /enable-feature /featurename:VirtualMachinePlatform /all /norestart
```

## 2. 下载并安装 WSL2 内核更新包
从微软官网下载：https://aka.ms/wsl2kernel

## 3. 设置 WSL2 为默认版本
```powershell
wsl --set-default-version 2
```

## 4. 下载 Docker Desktop for Windows
官网下载：https://www.docker.com/products/docker-desktop/

## 5. 安装完成后重启电脑

# 二、centos docker 安装

## 移除旧版本docker
```bash
sudo yum remove docker \
                  docker-client \
                  docker-client-latest \
                  docker-common \
                  docker-latest \
                  docker-latest-logrotate \
                  docker-logrotate \
                  docker-engine
```

## 配置docker yum源
```bash
sudo yum install -y yum-utils
sudo yum-config-manager \
--add-repo \
http://mirrors.aliyun.com/docker-ce/linux/centos/docker-ce.repo
```

## 安装最新 docker
```bash
sudo yum install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin
```

# 三、Ubuntu 安装docker

## 卸载旧版本
```bash
sudo apt-get remove docker docker-engine docker.io containerd runc
```

## 安装依赖包
```bash
sudo apt-get update
sudo apt-get install -y \
    ca-certificates \
    curl \
    gnupg \
    lsb-release
```

## 无法定位软件包 lsb-release 解决办法
出现“无法定位软件包 lsb-release”通常是因为软件源未更新或缺少 universe 源。你可以先尝试更新源并安装 lsb-release：

1. 更新软件源：
```bash
sudo apt-get update
```

2. 启用 universe 源（如果是 Ubuntu）：
```bash
sudo add-apt-repository universe
sudo apt-get update
```

3. 再次尝试安装 lsb-release：
```bash
sudo apt-get install lsb-release
```

如果还是不行，可以直接用如下命令获取系统代号（替代 lsb-release）：
```bash
cat /etc/os-release
source /etc/os-release && echo $VERSION_CODENAME
```

## 添加Docker官方GPG密钥
```bash
sudo mkdir -p /etc/apt/keyrings
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /etc/apt/keyrings/docker.gpg
```

## 设置稳定仓库
```bash
echo \
  "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/ubuntu \
  $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
```

## 安装Docker
```bash
sudo apt-get update
sudo apt-get install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin
```

## 启动&开机启动docker
```bash
systemctl enable docker --now
```

## 配置加速
```bash
sudo mkdir -p /etc/docker
sudo tee /etc/docker/daemon.json <<-'EOF'
{
    "registry-mirrors": [
        "http://hub-mirror.c.163.com",
        "https://mirrors.tuna.tsinghua.edu.cn",
        "http://mirrors.sohu.com",
        "https://ustc-edu-cn.mirror.aliyuncs.com",
        "https://ccr.ccs.tencentyun.com",
        "https://docker.m.daocloud.io",
        "https://docker.awsl9527.cn"
    ]
}
EOF
sudo systemctl daemon-reload
sudo systemctl restart docker
```

# 四、docker简单命令集

## 镜像操作

### 搜索镜像
```bash
docker search 镜像名
```

### 下载镜像
```bash
docker pull 镜像名[:版本号]  # 默认latest
```

### 查看已下载所有镜像
```bash
docker images
# 或
docker image ls
```

### 删除镜像
```bash
docker rmi 镜像名[:tag] 或 镜像ID
```

## 容器操作

### 运行一个新容器
```bash
docker run 镜像名[:tag]
docker run [参数] 镜像名
```
#### 运行容器参数
- `-d`：后台运行容器
- `--name 容器名`：指定容器名称
- `-p 主机端口:容器端口`：映射端口
- `-v 主机目录:容器目录`：挂载卷
- `--rm`：容器停止后自动删除
- `-e 环境变量名=值`：设置环境变量
- `--link 容器名:别名`：链接其他容器
- `--network 网络名`：指定网络连接
- `--restart 重启策略`：设置重启策略
- `--privileged`：获取容器的 root 权限
- `--cap-add`：添加容器的权限
- `--cap-drop`：移除容器的权限
- `--dns`：设置容器的 DNS 服务器
- `--dns-search`：设置容器的 DNS 搜索域
- `--entrypoint`：设置容器的入口点
- `--label`：添加元数据标签
- `--log-driver`：设置日志驱动程序
- `--log-opt`：设置日志驱动程序的选项
- `--pid`：设置容器的 PID 模式
- `--security-opt`：设置容器的安全选项
- `--stop-signal`：设置容器停止时发送的信号
- `--ulimit`：设置容器的资源限制



### 查看容器
```bash
docker ps           # 正在运行的容器
docker ps -a        # 所有容器
```

### 启动已有容器
```bash
docker start 容器ID或容器名
```

### 停止容器
```bash
docker stop 容器ID或容器名
```

### 重启容器
```bash
docker restart 容器ID或容器名
```

### 查看容器资源占用情况
```bash
docker stats 容器ID或容器名
```

### 查看容器日志
```bash
docker logs 容器ID或容器名
```

### 删除指定容器（要先stop容器）
```bash
docker rm 容器ID或容器名
```

### 强制删除指定容器（运行中的也可以删除）
```bash
docker rm -f 容器ID或容器名
```

### 后台启动容器
```bash
docker run -d --name mynginx nginx
```

### 后台启动并暴露端口
```bash
docker run -d --name mynginx -p 80:80 nginx
```

### 进入容器内部
```bash
docker exec -it 容器ID或容器名 /bin/bash
```
### 退出容器
```bash
exit
```


### 提交容器变化打成一个新的镜像
```bash
docker commit -m "update index.html" 你的容器名 你自定义的镜像名[:tag]
# 可选参数：-a "作者" -m "提交信息" -p -c
```

### 保存镜像为指定文件
```bash
docker save -o 你的包名.tar 镜像名[:tag]
```

### 删除多个镜像
```bash
docker rmi 镜像ID1 镜像ID2 ...
```

### 加载镜像
```bash
docker load -i tar包路径
```

### 登录 docker hub
```bash
docker login
# 按提示输入用户名和密码
```

### 重新给镜像打标签
```bash
docker tag 要上传的镜像[:tag] 用户名/新镜像名[:tag]
```

### 推送镜像
```bash
docker push 用户名/新镜像名[:tag]
```

## Docker Compose 使用

### 安装 Docker Compose
```bash
sudo curl -L "https://github.com/docker/compose/releases/download/1.29.2/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 检查安装
```bash
docker-compose --version
```

## 其他常用命令

### 查看所有已停止的容器
```bash
docker ps -a -f "status=exited"
```

### 删除所有已停止的容器
```bash
docker container prune -f
```

### 删除未被使用的镜像、网络和数据卷
```bash
docker system prune -a -f
```
# 五、常见错误
# 1. 权限问题
你遇到的报错 permission denied while trying to connect to the Docker daemon socket at unix:///var/run/docker.sock 是因为当前用户没有权限操作 Docker 服务。

解决方法如下：

1. 用 sudo 运行 docker 命令（推荐临时用法）：
```bash
sudo docker pull mysql
```

2. 或将当前用户加入 docker 用户组（推荐长期用法）：
```bash
sudo usermod -aG docker $USER
```
然后注销或重启，重新登录后即可直接用 docker 命令，无需 sudo。

3. 检查 Docker 服务是否已启动：
```bash
sudo systemctl status docker
```
如未启动，执行：
```bash
sudo systemctl start docker
```