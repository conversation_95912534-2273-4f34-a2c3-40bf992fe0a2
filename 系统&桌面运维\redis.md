# 设置 Redis 密码

在 Redis 中设置密码可以增强安全性，以下是设置密码的步骤：

## 1. 修改配置文件
编辑 Redis 的配置文件 `redis.conf`，找到以下行：
```conf
# requirepass foobared
```
取消注释并设置密码，例如：
```conf
requirepass yourpassword
```

## 2. 重启 Redis 服务
保存配置文件后，重启 Redis 服务以使更改生效：
```bash
redis-server /path/to/redis.conf
```

## 3. 使用密码连接 Redis
客户端连接 Redis 时需要提供密码：
```bash
redis-cli -a yourpassword
```

## 注意事项
- 确保密码足够复杂，避免使用弱密码。
- 如果 Redis 暴露在公网，请结合防火墙或其他安全措施保护实例。
