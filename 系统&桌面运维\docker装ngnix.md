# Docker 安装 Nginx （包含配置文件、日志目录的挂载）


## 一、 拉取 Nginx 镜像
```bash
docker pull nginx
```
## 二、 运行 Nginx 容器（默认 80 端口）
### 1.不需要配置挂载文件直接使用
```bash
docker run -d --name nginx -p 80:80 nginx
```
### 2.挂载配置文件和日志目录
注意：因为nginx是先加载一个主配置文件nginx.conf，所以你需要先在本地目录创建nginx.conf、default.conf文件，不然会出现创建失败的问题的
1)创建nginx.conf和default.conf方法：
1.可以直接传现有的nginx.conf、default.conf文件到本地目录
2.先执行上面1不需要配置挂载文件直接使用的命令，创建好容器后，将nginx.conf 和default.conf从容器内复制出来

语句：

```
docker cp 你的容器名:/etc/nginx/conf.d/default.conf 你的本地目录 
docker cp 你的容器名:/etc/nginx/nginx.conf 你的本地目录
```

例如

```
docker cp nginx:/etc/nginx/conf.d/default.conf /home/<USER>/config
docker cp nginx:/etc/nginx/nginx.conf /home/<USER>/config
```

然后删除现有的nginx 容器

```
docker rm -f nginx
```

2)创建容器

```bash
docker run  --name mynginx -p 80:80 \
-v /你的本地目录/nginx.conf:/etc/nginx/nginx.conf \
-v /你的本地目录/logs:/var/log/nginx \
-v /你的本地目录/default.conf:/etc/nginx/conf.d/default.conf \
-v /你的本地目录/html:/usr/share/nginx/html -d nginx
```
例如
```bash
docker run --name nginx -p 80:80  --restart always \
-v /home/<USER>/config/nginx.conf:/etc/nginx/nginx.conf:ro \
-v /home/<USER>/html:/usr/share/nginx/html:rw \
-v /home/<USER>/config/default.conf:/etc/nginx/conf.d/default.conf:ro \
-v /home/<USER>/log:/var/log/nginx -d nginx
```

## 三、 浏览器访问
在本机浏览器输入：http://localhost 或 http://服务器IP

## 四、 常用操作
### 查看容器
```bash
docker ps
```
### 进入 Nginx 容器
```bash
docker exec -it nginx /bin/bash
```
### 停止容器
```bash
docker stop nginx
```
### 启动容器
```bash
docker start nginx
```
### 删除容器
```bash
docker rm -f nginx
```

