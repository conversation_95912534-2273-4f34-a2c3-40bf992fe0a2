# CMD 指令大集

## 基本命令
- `dir`：列出当前目录下的文件和文件夹。
- `cd [路径]`：切换目录。
- `md [文件夹名]`：创建新文件夹。
- `del [文件名]`：删除文件。
- `rmdir [文件夹名] /s /q`：删除文件夹及其内容。
- `copy [源文件] [目标路径]`：复制文件。
- `move [源文件] [目标路径]`：移动文件。

## 系统信息
- `ipconfig`：查看网络配置信息。
- `systeminfo`：查看系统信息。
- `tasklist`：查看当前运行的进程。
- `taskkill /im [进程名] /f`：强制结束进程。

## 文件操作
- `type [文件名]`：查看文件内容。
- `ren [旧文件名] [新文件名]`：重命名文件。
- `attrib [文件名]`：查看或修改文件属性。

## 网络相关
- `ping [地址]`：测试网络连通性。
- `netstat -an`：查看网络连接状态。
- `tracert [地址]`：跟踪路由路径。

## 高级命令
- `chkdsk [盘符:]`：检查磁盘错误。
- `sfc /scannow`：扫描并修复系统文件。
- `shutdown /s /t [秒]`：定时关机。
- `shutdown /a`：取消关机。

## 帮助命令
- `[命令] /?`：查看命令的帮助信息。
- `help`：列出所有可用命令。
