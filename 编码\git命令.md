# 常用 Git 命令

## 配置
```bash
git config --global user.name "你的名字"
git config --global user.email "你的邮箱"
```

## 仓库操作
```bash
git init                      # 初始化仓库
git clone 仓库地址            # 克隆远程仓库
```

## 文件操作
```bash
git status                    # 查看状态
git add 文件名                # 添加到暂存区
git add .                     # 添加所有文件
git commit -m "提交说明"      # 提交更改
```

## 分支操作
```bash
git branch                    # 查看分支
git branch 分支名             # 创建分支
git checkout 分支名           # 切换分支
git checkout -b 分支名        # 创建并切换分支
git merge 分支名              # 合并分支
git branch -d 分支名          # 删除分支
```

## 远程操作
```bash
git remote -v                 # 查看远程
git remote add origin 地址    # 添加远程仓库
git push origin 分支名        # 推送到远程
git pull                      # 拉取远程仓库最新代码
```

## 其他常用
```bash
git log                       # 查看提交历史
git diff                      # 查看差异
git checkout -- 文件名        # 撤销修改
git reset --hard HEAD         # 恢复到上一次提交
```

## 常见错误及解决方法

### fatal: not a git repository (or any of the parent directories): .git
出现这个错误说明当前目录不是一个 git 仓库。

**解决方法：**
1. 如果你要在当前目录初始化 git 仓库：
```bash
git init
```
2. 如果你要在已有仓库操作，请先进入正确的仓库目录。
3. 如果是克隆远程仓库：
```bash
git clone 仓库地址
```
