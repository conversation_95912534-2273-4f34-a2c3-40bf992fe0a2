# Nginx 基础教学

## 1. Nginx 简介
Nginx 是一款高性能的 Web 服务器、反向代理服务器及邮件代理服务器，常用于网站静态资源服务、负载均衡、反向代理等场景。

## 2. 常用命令
```bash
# 启动 Nginx
sudo nginx
# 检查配置文件是否有误
sudo nginx -t
# 重新加载配置（不中断服务）
sudo nginx -s reload
# 停止 Nginx
sudo nginx -s stop
# 查看 Nginx 进程
ps -ef | grep nginx
```

## 3. 配置文件结构
Nginx 主配置文件通常为 /etc/nginx/nginx.conf，结构如下：
```nginx
user  nginx;
worker_processes  1;

error_log  /var/log/nginx/error.log warn;
pid        /var/run/nginx.pid;

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    sendfile        on;
    keepalive_timeout  65;

    server {
        listen       80;
        server_name  localhost;

        location / {
            root   /usr/share/nginx/html;
            index  index.html index.htm;
        }
    }
}
```

## 4. 常见应用场景
### 4.1 静态网站服务
将静态网页文件放到 /usr/share/nginx/html 目录下，浏览器访问服务器 IP 即可访问。

### 4.2 反向代理
```nginx
server {
    listen 80;
    server_name yourdomain.com;
    location / {
        proxy_pass http://127.0.0.1:8080;
    }
}
```

### 4.3 负载均衡
```nginx
http {
    upstream backend {
        server ************;
        server ************;
    }
    server {
        listen 80;
        location / {
            proxy_pass http://backend;
        }
    }
}
```

## 5. 配置热更新
修改配置后无需重启服务，执行：
```bash
sudo nginx -s reload
```

---
如需更详细的 Nginx 配置、HTTPS、动静分离、限流等进阶用法，欢迎继续提问！

# 六、Nginx 进阶用法

## 1. 配置 HTTPS（SSL）
```nginx
server {
    listen 443 ssl;
    server_name yourdomain.com;
    ssl_certificate     /etc/nginx/ssl/yourdomain.crt;
    ssl_certificate_key /etc/nginx/ssl/yourdomain.key;
    ssl_protocols       TLSv1.2 TLSv1.3;
    ssl_ciphers         HIGH:!aNULL:!MD5;
    location / {
        proxy_pass http://127.0.0.1:8080;
    }
}
# 80端口自动跳转到443
server {
    listen 80;
    server_name yourdomain.com;
    return 301 https://$host$request_uri;
}
```

## 2. 动静分离
```nginx
server {
    listen 80;
    server_name yourdomain.com;
    location /static/ {
        alias /var/www/static/;
    }
    location / {
        proxy_pass http://127.0.0.1:8080;
    }
}
```

## 3. 访问控制与限流
### IP 白名单
```nginx
location /admin/ {
    allow ***********/24;
    deny all;
}
```
### 限流配置
```nginx
http {
    limit_req_zone $binary_remote_addr zone=one:10m rate=1r/s;
    server {
        location /api/ {
            limit_req zone=one burst=5 nodelay;
        }
    }
}
```

## 4. 反向代理 WebSocket
```nginx
location /ws/ {
    proxy_pass http://127.0.0.1:6001;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "Upgrade";
    proxy_set_header Host $host;
}
```

## 5. 日志切割与格式化
```nginx
http {
    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';
    access_log  /var/log/nginx/access.log  main;
}
```

---
如需更多 Nginx 进阶配置（如反向代理多端口、缓存、rewrite、健康检查等），可继续提问！
